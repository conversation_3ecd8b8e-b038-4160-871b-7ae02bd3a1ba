<?php

// Test RSS content similar to what you're seeing
$rssContent = '<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
    <channel>
        <item>
            <title>73 Hilariously Embarrassing <PERSON>\'s Appointments That Went Off The Rails</title>
            <link>https://www.boredpanda.com/embarrassing-moments-doctors-office/</link>
            <comments>https://www.boredpanda.com/embarrassing-moments-doctors-office/#respond</comments>
        </item>
    </channel>
</rss>';

echo "Original RSS content:\n";
echo $rssContent . "\n\n";

// Parse the XML
libxml_use_internal_errors(true);
$dom = new DOMDocument();
$dom->loadXML($rssContent);

$items = $dom->getElementsByTagName('item');
foreach ($items as $item) {
    $link = $item->getElementsByTagName('link')->item(0);
    
    echo "Link element found: " . ($link ? 'YES' : 'NO') . "\n";
    
    if ($link) {
        echo "Link nodeValue: '" . $link->nodeValue . "'\n";
        echo "Link nodeValue hex: " . bin2hex($link->nodeValue) . "\n";
        echo "Has href attribute: " . ($link->hasAttribute('href') ? 'YES' : 'NO') . "\n";
        
        if ($link->hasAttribute('href')) {
            echo "href attribute: '" . $link->getAttribute('href') . "'\n";
        }
    }
}
