<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Services\BypassCloudflareService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Mockery;
use Tests\TestCase;

class BypassCloudflareServiceTest extends TestCase {
    public function testItGetRawHtmlOrRss(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $fakeResponse = new Response(200, [], '<html><body><h1>Test</h1></body></html>');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new BypassCloudflareService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
        $this->assertEquals($fakeResponse->getBody(), $result->getRawContent());
    }

    public function testItThrowsExceptionAtEmptyContent(): void {
        $this->expectException(\App\Exceptions\APIException::class);

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $fakeResponse = new Response(200, [], '');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new BypassCloudflareService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItThrowsExceptionAtGuzzleException(): void {
        $this->expectException(\App\Exceptions\APIException::class);

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new RequestException('Guzzle error', new Request('POST', 'test')));

        $service = new BypassCloudflareService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItReturnsNullOnTimeout(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new ConnectException('timeout', new Request('POST', 'test')));

        $service = new BypassCloudflareService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
        $this->assertNull($result);
    }

    public function testItReturnsNullOnPublisherRssException(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $response   = new Response(404, [], 'Not Found');
        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new \GuzzleHttp\Exception\ClientException('Not Found', new Request('POST', 'test'), $response));

        $service = new BypassCloudflareService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);

        $this->assertNull($result);
    }

    public function testItThrowsAPIExceptionOnOtherClientErrors(): void {
        $this->expectException(\App\Exceptions\APIException::class);

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $response   = new Response(400, [], 'Bad Request');
        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new \GuzzleHttp\Exception\ClientException('Bad Request', new Request('POST', 'test'), $response));

        $service = new BypassCloudflareService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItReturnsNullOnServerErrorPublisherRssException(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $response   = new Response(503, [], 'Service Unavailable');
        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new \GuzzleHttp\Exception\ServerException('Service Unavailable', new Request('POST', 'test'), $response));

        $service = new BypassCloudflareService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);

        $this->assertNull($result);
    }

    public function testItThrowsAPIExceptionOnOtherServerErrors(): void {
        $this->expectException(\App\Exceptions\APIException::class);

        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();

        $response   = new Response(405, [], 'Method Not Allowed');
        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andThrow(new \GuzzleHttp\Exception\ServerException('Method Not Allowed', new Request('POST', 'test'), $response));

        $service = new BypassCloudflareService($clientMock);
        $service->getRawHtmlOrRss($endpointUrl, $customUserAgent);
    }

    public function testItCanHandleCustomCookies(): void {
        $endpointUrl     = $this->generator->url();
        $customUserAgent = $this->generator->word();
        $customCookies   = ['session' => 'abc123'];

        $fakeResponse = new Response(200, [], '<html><body><h1>Test</h1></body></html>');

        $clientMock = Mockery::mock(Client::class);
        $clientMock->shouldReceive('request')
            ->once()
            ->andReturn($fakeResponse);

        $service = new BypassCloudflareService($clientMock);
        $result  = $service->getRawHtmlOrRss($endpointUrl, $customUserAgent, $customCookies);
        $this->assertEquals($fakeResponse->getBody(), $result->getRawContent());
    }

    public function testItReturnsNullWhenMakeRequestReturnsNull(): void {
        $endpointUrl = $this->generator->url();

        // Create anonymous class that extends BypassCloudflareService and overrides makeRequest to return null
        $clientMock = Mockery::mock(Client::class);
        $service    = new class($clientMock) extends BypassCloudflareService {
            protected function makeRequest(array $requestData): ?\Psr\Http\Message\ResponseInterface {
                return null;
            }
        };

        $result = $service->getRawHtmlOrRss($endpointUrl);

        $this->assertNull($result);
    }
}
