<?php

declare(strict_types=1);

namespace Tests\Unit\Helpers;

use App\Helpers\ContentHelper;
use PHPUnit\Framework\TestCase;

class ContentHelperTest extends TestCase {
    public function testItIsContentRss(): void {
        $contentHelper = new ContentHelper();

        $this->assertTrue($contentHelper->isContentRss('<rss version="2.0"></rss>'));
        $this->assertTrue($contentHelper->isContentRss('<feed></feed>'));

        $this->assertFalse($contentHelper->isContentRss('<html></html>'));
        $this->assertFalse($contentHelper->isContentRss('<!DOCTYPE html><html></html>'));
        $this->assertFalse($contentHelper->isContentRss(''));
    }

    public function testItSlugifyTitle(): void {
        $contentHelper = new ContentHelper();

        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World?'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World.'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World...'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World???'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...?!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...?!?'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...?!?!'));
        $this->assertEquals('hello-world', $contentHelper->slugifyTitle('Hello World!!!???...?!?!?'));
    }

    public function testItGetCanonicalUrl(): void {
        $contentHelper = new ContentHelper();

        $this->assertEquals('https://example.com', $contentHelper->getCanonicalUrl('https://example.com'));
        $this->assertEquals('https://example.com', $contentHelper->getCanonicalUrl('https://example.com/'));
        $this->assertEquals('https://example.com/path', $contentHelper->getCanonicalUrl('https://example.com/path'));
        $this->assertEquals('https://example.com/path', $contentHelper->getCanonicalUrl('https://example.com/path/'));
        $this->assertEquals('https://example.com/path/to/page', $contentHelper->getCanonicalUrl('https://example.com/path/to/page'));
        $this->assertEquals('https://example.com/path/to/page', $contentHelper->getCanonicalUrl('https://example.com/path/to/page?query=string'));
        $this->assertEquals('https://example.com/path/to/page', $contentHelper->getCanonicalUrl('https://example.com/path/to/page?query=string#anchor'));
    }

    public function testItGetCanonicalUrlWithPreservedParams(): void {
        $contentHelper = new ContentHelper();

        $this->assertEquals('https://example.com/path?NewsID=123',
            $contentHelper->getCanonicalUrl('https://example.com/path?NewsID=123&utm_source=google&ref=twitter'));
        $this->assertEquals('https://example.com/path',
            $contentHelper->getCanonicalUrl('https://example.com/path?utm_source=google&category=news&ref=twitter'));
        $this->assertEquals('https://example.com/path?NewsID=123',
            $contentHelper->getCanonicalUrl('https://example.com/path?utm_source=google&NewsID=123'));
    }

    public function testItGetCanonicalUrlWithMalformedUrls(): void {
        $contentHelper = new ContentHelper();

        // Test with malformed URLs that don't have a host
        $this->assertEquals('malformed-url', $contentHelper->getCanonicalUrl('malformed-url'));
        $this->assertEquals('://missing-host', $contentHelper->getCanonicalUrl('://missing-host'));
        $this->assertEquals('/path/only', $contentHelper->getCanonicalUrl('/path/only'));
        $this->assertEquals('', $contentHelper->getCanonicalUrl(''));

        // Test with URLs that have invalid schemes or structures
        $this->assertEquals('ftp://example.com', $contentHelper->getCanonicalUrl('ftp://example.com'));
        $this->assertEquals('javascript:void(0)', $contentHelper->getCanonicalUrl('javascript:void(0)'));
        $this->assertEquals('mailto:<EMAIL>', $contentHelper->getCanonicalUrl('mailto:<EMAIL>'));

        // Test with URLs that have HTML entities but still malformed
        $this->assertEquals('malformed&amp;url', $contentHelper->getCanonicalUrl('malformed&amp;url'));
    }

    public function testIsContentRssHandlesEdgeCases(): void {
        $contentHelper = new ContentHelper();

        $this->assertFalse($contentHelper->isContentRss('<rss><unclosed>'));
        $this->assertFalse($contentHelper->isContentRss('<invalid xml'));
        $this->assertTrue($contentHelper->isContentRss('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"></rdf:RDF>'));
        $this->assertTrue($contentHelper->isContentRss('<RSS version="2.0"></RSS>'));
        $this->assertTrue($contentHelper->isContentRss('<FEED></FEED>'));
    }

    public function testCleanRawData(): void {
        $contentHelper = new ContentHelper();

        $this->assertEquals('<html><body><h1>Test</h1></body></html>', $contentHelper->cleanRawData('<html><body><h1>Test</h1></body></html>'));
        $this->assertEquals('<html><body><h1>Test</h1></body><script></script></html>', $contentHelper->cleanRawData('<html><body><h1>Test</h1></body></html><script></script>'));
        $this->assertEquals('<html><body><h1>Test</h1></body></html>', $contentHelper->cleanRawData('<html><body><h1>Test</h1></body></html><style></style>'));
    }

    public function testGetContentFromRawRssItemWithContentEncoded(): void {
        $contentHelper = new ContentHelper();

        $rawArticleData = '<item><title>Test</title><link>http://test.com</link><description>Test</description><content:encoded>Test</content:encoded></item>';

        $result = $contentHelper->getContentFromRawRssItem($rawArticleData);
        $this->assertEquals('Test', $result['full_content']);
        $this->assertEquals('<item><title>Test</title><link>http://test.com</link><description>Test</description></item>', $result['filtered_content']);
    }

    public function testGetContentFromRawRssItemWithContent(): void {
        $contentHelper = new ContentHelper();

        $rawArticleData = '<item><title>Test</title><link>http://test.com</link><description>Test</description><content>Test</content></item>';

        $result = $contentHelper->getContentFromRawRssItem($rawArticleData);
        $this->assertEquals('Test', $result['full_content']);
        $this->assertEquals('<item><title>Test</title><link>http://test.com</link><description>Test</description></item>', $result['filtered_content']);
    }

    public function testGetContentFromRawRssItemWithDescription(): void {
        $contentHelper = new ContentHelper();

        $rawArticleData = '<item><title>Test</title><link>http://test.com</link><description>Test</description></item>';

        $result = $contentHelper->getContentFromRawRssItem($rawArticleData);
        $this->assertEquals('Test', $result['full_content']);
        $this->assertEquals('<item><title>Test</title><link>http://test.com</link></item>', $result['filtered_content']);
    }

    public function testGetBodyFromHtmlContent(): void {
        $contentHelper = new ContentHelper();

        $rawHtmlContent = '<html><body><h1>Test</h1></body></html>';

        $result = $contentHelper->getBodyFromHtmlContent($rawHtmlContent);
        $this->assertEquals('<body><h1>Test</h1></body>', $result);
    }

    public function testGetBodyFromHtmlContentWithNoBody(): void {
        $contentHelper = new ContentHelper();

        $rawHtmlContent = '<html><h1>Test</h1></html>';

        $result = $contentHelper->getBodyFromHtmlContent($rawHtmlContent);
        $this->assertEquals('', $result);
    }

    public function testDecodeHtmlEntities(): void {
        $contentHelper = new ContentHelper();

        $this->assertEquals('Test & Example', $contentHelper->decodeHtmlEntities('Test &amp; Example'));
        $this->assertEquals('Test "quoted" text', $contentHelper->decodeHtmlEntities('Test &quot;quoted&quot; text'));
        $this->assertEquals('Test <tag> content', $contentHelper->decodeHtmlEntities('Test &lt;tag&gt; content'));
        $this->assertEquals('Test \'single\' quotes', $contentHelper->decodeHtmlEntities('Test &#39;single&#39; quotes'));
        $this->assertEquals('Test', $contentHelper->decodeHtmlEntities('Test'));
        $this->assertEquals('', $contentHelper->decodeHtmlEntities(''));
    }
}
