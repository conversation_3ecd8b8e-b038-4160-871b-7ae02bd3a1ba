<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\Constants\Parser;
use App\Exceptions\ServiceException;
use App\Helpers\ContentHelper;
use App\Repositories\PublisherEndpointRepository;
use App\Repositories\PublisherRepository;
use Throwable;

class SanitizesParsedContent {
    public function __construct(
        private PublisherRepository $publisherRepository,
        private PublisherEndpointRepository $publisherEndpointRepository,
        private ContentHelper $contentHelper,
    ) {
    }

    /**
     * @throws ServiceException
     */
    public function execute(array $parsedContent, int $publisherId, int $channelId): array {
        try {
            $parsedContent['title']          = $this->sanitizeTitle($parsedContent['title']);
            $parsedContent['description']    = $this->sanitizeDescription($parsedContent['description'] ?? '');
            $parsedContent['full_content']   = $this->sanitizeContent($parsedContent['full_content'], $parsedContent[Parser::COVER_IMAGE_URL], $publisherId, $channelId, $parsedContent['title'], $parsedContent['link']);
            $parsedContent['link']           = $this->sanitizeLink($parsedContent['link']);
            $parsedContent['canonical_url']  = $this->contentHelper->getCanonicalUrl($parsedContent['link']);

            return $parsedContent;
        } catch (Throwable $exception) {
            throw new ServiceException($exception->getCode(), 'Failed to sanitize content' . $exception->getMessage());
        }
    }

    protected function addGaTrackingIntoContent(string $content, int $publisherId, string $link, string $title): string {
        $iframes = '';
        $gaid    = $this->publisherRepository->find($publisherId)?->ga_id;

        if ($gaid) {
            if (preg_match('/^UA-\d{4,10}-\d+$/', $gaid)) {
                $encodedPath = urlencode($this->getPath($link));
                $iframes .= '<iframe src="https://cdn.newswav.com/global-track5.html?path=' . $encodedPath
                    . '&ga-id=' . $gaid
                    . '&title=' . urlencode($title)
                    . '&utm_source=Newswav&utm_medium=App" height="0" frameBorder="0"></iframe>';
            } else {
                $encodedPath = urlencode($this->getPath($link));
                $iframes .= '<iframe src="https://cdn.newswav.com/ga4-track.html?path=' . $encodedPath
                    . '&ga-id=' . $gaid
                    . '&title=' . urlencode($title)
                    . '&utm_source=Newswav&utm_medium=App" height="0" frameBorder="0"></iframe>';

                $debugGA = 'https://cdn.newswav.com/ga4-v2-track.html?path=' . urlencode($link)
                    . '&ga-id=G-FG1LT8DMNS'
                    . '&title=' . urlencode($title)
                    . '&utm_source=ga4-v2&utm_medium=Newswav-App&utm_campaign=';

                $iframes .= '<iframe src="' . $debugGA . '" height="0" frameBorder="0"></iframe>';
            }
        }

        return $iframes . $content;
    }

    protected function sanitizeTitle(string $title): string {
        return strip_tags(html_entity_decode($title, ENT_QUOTES | ENT_HTML5, 'UTF-8'));
    }

    protected function sanitizeDescription(string $description): string {
        $words = preg_split('/\s+/', strip_tags($description));
        if (count($words) > Parser::MAX_DESCRIPTION_WORD_COUNT) {
            $description = implode(' ', array_slice($words, 0, Parser::MAX_DESCRIPTION_WORD_COUNT)) . '...';
        }

        return $description;
    }

    protected function sanitizeContent(string $content, array $coverImage, int $publisherId, int $channelId, string $title, string $link): string {
        $content = $this->ensureContentWrappedInParagraph($content);

        $hasProxyImage = $this->publisherEndpointRepository->getHasProxyImage($publisherId, $channelId);

        $imageResult = $this->addFirstImage($content, $coverImage, $title, $hasProxyImage);
        $content     = $imageResult['content'];

        $content       = preg_replace('#<style\b[^>]*>(.*?)</style>#is', '', $content);
        $content       = preg_replace('/[\t\n\r]+/', '', $content);
        $content       = $this->processImagesInContent($content, $hasProxyImage, $imageResult['coverImageAdded']);
        $androidScript = '<script type="text/javascript">function showImageDetail(position) { Android.showImage(position); }</script>';
        $content       = $content . $androidScript;

        return $this->insertAdsIntoContent($this->addGaTrackingIntoContent($content, $publisherId, $link, $title));
    }

    protected function addFirstImage(string $content, array $coverImage, string $title, bool $hasProxyImage): array {
        $coverImageAdded = false;

        if (empty($coverImage['url']) === false && strpos($content, $coverImage['url']) === false) {
            $escapedUrl     = htmlspecialchars($coverImage['url'], ENT_QUOTES);
            $escapedCaption = htmlspecialchars($coverImage['caption'] ?? '', ENT_QUOTES);
            $escapedTitle   = htmlspecialchars($title, ENT_QUOTES);

            $coverImageHtml = "<p><figure style='text-align: center;'>";

            if ($hasProxyImage) {
                $host = config('image_proxy.host');
                $coverImageHtml .= "<img onclick=\"showImageDetail('0')\""
                    . " src='{$host}/1000x0,q50=/{$escapedUrl}'"
                    . " style='width:100%' class='fullWidthImg'"
                    . ' srcset="'
                    . "{$host}/400x0,q50=/{$escapedUrl} 400w, "
                    . "{$host}/768x0,q50=/{$escapedUrl} 768w, "
                    . "{$host}/1000x0,q50=/{$escapedUrl} 1000w\""
                    . " alt='{$escapedTitle}'>";
            } else {
                $coverImageHtml .= "<img onclick=\"showImageDetail('0')\""
                    . " src='{$escapedUrl}'"
                    . " style='width:100%' class='fullWidthImg'"
                    . " alt='{$escapedTitle}'>";
            }

            $coverImageHtml .= "<figcaption> {$escapedCaption} </figcaption>";
            $coverImageHtml .= '</figure></p>';

            $content         = $coverImageHtml . $content;
            $coverImageAdded = true;
        }

        return [
            'content'         => $content,
            'coverImageAdded' => $coverImageAdded,
        ];
    }

    protected function processImagesInContent(string $content, bool $hasProxyImage, bool $coverImageAdded = false): string {
        $imageCounter = $coverImageAdded ? 1 : 0;

        $content = preg_replace_callback('/<img([^>]*)>/i', function ($matches) use ($hasProxyImage, &$imageCounter) {
            $imgAttributes = $matches[1];

            if (preg_match('/src\s*=\s*["\']([^"\']+)["\']/i', $imgAttributes, $srcMatches)) {
                $originalUrl = $srcMatches[1];

                if ($hasProxyImage) {
                    $host    = config('image_proxy.host');
                    $width   = config('image_proxy.regular_width');
                    $quality = config('image_proxy.regular_quality');

                    if ( ! str_starts_with($originalUrl, $host)) {
                        $proxyUrl      = "{$host}/{$width}x0,q{$quality}=/{$originalUrl}";
                        $imgAttributes = preg_replace('/src\s*=\s*["\'][^"\']+["\']/i', "src='{$proxyUrl}'", $imgAttributes);
                    }
                }
            }

            if ( ! preg_match('/onclick\s*=/i', $imgAttributes)) {
                $imgAttributes .= " onClick=\"showImageDetail('{$imageCounter}')\"";
                $imageCounter++;
            }

            return "<img{$imgAttributes}>";
        }, $content);

        return $content;
    }

    protected function insertAdsIntoContent(string $content): string {
        $parts      = preg_split('/(<\/p>)/i', $content, -1, PREG_SPLIT_DELIM_CAPTURE);
        $blocks     = array_chunk($parts, 2);
        $blockCount = count($blocks);

        $adsToInsert = max(1, min(Parser::MAX_ADS_COUNT, $blockCount - 1));
        $interval    = floor($blockCount / ($adsToInsert + 1));

        $newContent = '';
        foreach ($blocks as $index => $blockPair) {
            $newContent .= implode('', $blockPair);

            if (
                $interval > 0
                && $adsToInsert > 0
                && ($index + 1) % $interval === 0
                && ($index + 1) !== $blockCount
            ) {
                $newContent .= '<!--AD-->';
                $adsToInsert--;
            }
        }

        return $newContent;
    }

    protected function getPath(string $url): string {
        return parse_url($url, PHP_URL_PATH) ?? '';
    }

    protected function sanitizeLink(string $link): string {
        $link = $this->contentHelper->getCanonicalUrl($link);

        return $link . (strpos($link, '?') === false ? '?' : '&') . Parser::SOURCE_TRACKING_URL_QUERY_PARAMETER;
    }

    protected function ensureContentWrappedInParagraph(string $content): string {
        $content = preg_replace('/\<[\/]{0,1}div[^\>]*\>/i', '', trim($content));

        if (empty($content) === true) {
            return $content;
        }
        $decoded = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        if (stripos($content, '<p') === false && stripos($decoded, '<p') === false) {
            return '<p>' . $decoded . '</p>';
        }

        return $decoded;
    }
}
