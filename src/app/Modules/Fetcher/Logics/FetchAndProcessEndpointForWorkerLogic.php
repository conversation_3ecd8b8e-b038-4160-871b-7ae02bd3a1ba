<?php

declare(strict_types=1);

namespace App\Modules\Fetcher\Logics;

use App\Helpers\ContentHelper;
use App\Modules\Fetcher\Services\PassesArticleDataToParserAdder;
use App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent;
use App\Repositories\PublisherEndpointRepository;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Illuminate\Support\Facades\Log;

class FetchAndProcessEndpointForWorkerLogic {
    public function __construct(
        private RetrievesArticleDataFromRawContent $retrievesArticleDataFromRawContentService,
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private GuzzleReadService $guzzleReadService,
        private PassesArticleDataToParserAdder $passesArticleDataToParserAdderService,
        private ContentHelper $contentHelper,
        private PublisherEndpointRepository $publisherEndpointsRepository,
    ) {
    }

    public function execute(string $workerId): void {
        $endpointsWithCrawlerSettings = $this->publisherEndpointsRepository->getCrawlerSettingsWithEndpointsByWorkerId($workerId);

        foreach ($endpointsWithCrawlerSettings as $endpoint) {

            Log::info('Fetching articles for endpoint ' . $endpoint->endpoint);
            if ($endpoint->crawlerSetting->use_headless_browser === true) {
                $rawHtmlOrRss = $this->headlessBrowserService->getRawHtmlOrRss(
                    $endpoint->endpoint,
                    $endpoint->crawlerSetting->custom_user_agent
                );
            } elseif ($endpoint->crawlerSetting->to_puppeteer === true) {
                $rawHtmlOrRss = $this->bypassCloudflareService->getRawHtmlOrRss(
                    $endpoint->endpoint,
                    $endpoint->crawlerSetting->custom_user_agent,
                    $endpoint->crawlerSetting->custom_cookies,
                );
            } else {
                $rawHtmlOrRss = $this->guzzleReadService->getRawHtmlOrRss(
                    $endpoint->endpoint,
                    $endpoint->crawlerSetting->custom_user_agent,
                );
            }

            if ($rawHtmlOrRss === null) {
                // $this->publisherEndpointsRepository->updateLastChecked($endpoint->id);

                continue;
            }

            $articleDataArray = $this->retrievesArticleDataFromRawContentService->execute($rawHtmlOrRss);

            if ($articleDataArray === []) {
                Log::info('No article data found for endpoint ' . $endpoint->endpoint);
                // $this->publisherEndpointsRepository->updateLastChecked($endpoint->id);

                continue;
            }

            $this->passesArticleDataToParserAdderService->execute(
                $endpoint->crawlerSetting->publisher_id,
                $endpoint->crawlerSetting->channel_id,
                $articleDataArray,
                $this->contentHelper->isContentRss($rawHtmlOrRss->getRawContent()),
                $endpoint->crawlerSetting->custom_prompt,
                $endpoint->crawlerSetting->to_puppeteer,
                $endpoint->crawlerSetting->use_headless_browser,
                $endpoint->use_ai_parsing
            );
            // $this->publisherEndpointsRepository->updateLastChecked($endpoint->id);
            Log::info('Successfully processed endpoint ' . $endpoint->endpoint);
        }
    }
}
