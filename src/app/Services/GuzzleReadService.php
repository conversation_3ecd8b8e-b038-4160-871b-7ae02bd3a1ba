<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\Constants\ServerParameters;
use App\Classes\ValueObjects\RawContentObject;
use App\Exceptions\APIException;
use App\Exceptions\PublisherRssException;
use Exception;
use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use Psr\Http\Message\ResponseInterface;

class GuzzleReadService {
    public function __construct(private Client $client) {
    }

    /**
     * @throws APIException
     *
     * @return RawContentObject|null raw html or rss feed, null if PublisherRssException occurs
     */
    public function getRawHtmlOrRss(string $endpointUrl, ?string $customUserAgent = null): ?RawContentObject {
        try {
            $response = $this->makeRequest($endpointUrl, $customUserAgent);

            if ($response === null) {
                return null;
            }

            $content = $response->getBody()->getContents();
            if (empty($content) === true) {
                throw new APIException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, 'Guzzle Read Service returned empty response for URL: ' . $endpointUrl);
            }

            return new RawContentObject($content);
        } catch (PublisherRssException $exception) {
            return null;
        } catch (Exception $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }

    protected function makeRequest(string $endpointUrl, ?string $customUserAgent = null): ?ResponseInterface {
        try {
            $response =  $this->client->request(ServerParameters::HTTP_METHOD_GET, $endpointUrl, [
                'headers' => [
                    'User-Agent' => $customUserAgent ?? 'newswav-fetcher',
                ],
                'timeout' => 30,
            ]);

            return $response;
        } catch (ConnectException $exception) {
            throw new PublisherRssException($exception->getCode(), 'Request timeout', $endpointUrl);
        } catch (ClientException $exception) {
            $statusCode = $exception->getResponse()->getStatusCode();

            if (in_array($statusCode, [ServerParameters::HTTP_STATUS_NOT_FOUND, ServerParameters::HTTP_STATUS_FORBIDDEN, 410], true)) {
                throw new PublisherRssException($statusCode, $exception->getMessage(), $endpointUrl);
            }

            throw new APIException($exception->getCode(), $exception->getMessage());
        } catch (ServerException $exception) {
            $statusCode = $exception->getResponse()->getStatusCode();

            if (in_array($statusCode, [ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, ServerParameters::HTTP_STATUS_BAD_GATEWAY, ServerParameters::HTTP_STATUS_SERVICE_UNAVAILABLE, ServerParameters::HTTP_STATUS_GATEWAY_TIMEOUT], true)) {
                throw new PublisherRssException($statusCode, $exception->getMessage(), $endpointUrl);
            }

            throw new APIException($exception->getCode(), $exception->getMessage());
        } catch (GuzzleException $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }
}
