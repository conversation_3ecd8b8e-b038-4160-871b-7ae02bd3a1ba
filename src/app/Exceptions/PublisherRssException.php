<?php

declare(strict_types=1);

namespace App\Exceptions;

use Illuminate\Support\Facades\Log;

class PublisherRssException extends BaseException {
    public function __construct(int $statusCode, string $errorMessage = '', ?string $url = null) {
        parent::__construct($statusCode, $errorMessage);

        $logMessage = "PublisherRssException thrown: {$errorMessage} (Status: {$statusCode})";
        if ($url !== null) {
            $logMessage .= " for URL: {$url}";
        }

        Log::warning($logMessage);
    }
}
