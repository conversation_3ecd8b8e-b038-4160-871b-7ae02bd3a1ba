<?php

declare(strict_types=1);

namespace App\Exceptions;

use App\Classes\Constants\ServerParameters;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Sentry\Laravel\Integration;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler {
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void {
        $this->reportable(function (Throwable $exception): void {
            if ($this->shouldReport($exception)) {
                Log::error($exception->getMessage(), [
                    'exception' => $exception,
                ]);
                Integration::captureUnhandledException($exception);
            }
        });

        $this->renderable(function (Throwable $exception): JsonResponse {
            if ($exception instanceof NotFoundHttpException) {
                return response()->json([
                    'status'  => ServerParameters::HTTP_STATUS_NOT_FOUND,
                    'message' => 'Resource not found',
                ], ServerParameters::HTTP_STATUS_NOT_FOUND);
            }

            if ($exception instanceof ValidationException) {
                return response()->json([
                    'status'  => ServerParameters::HTTP_STATUS_UNPROCESSABLE_ENTITY,
                    'message' => $exception->getMessage(),
                    'errors'  => $exception->errors(),
                ], ServerParameters::HTTP_STATUS_UNPROCESSABLE_ENTITY);
            }

            if ($exception instanceof BaseException) {
                return response()->json([
                    'status'  => $exception->getStatusCode(),
                    'message' => $exception->getLocalisedMessage(),
                ], $exception->getStatusCode());
            }

            return response()->json([
                'status'  => ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR,
                'message' => $exception->getMessage(),
            ], ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR);
        });
    }
}
