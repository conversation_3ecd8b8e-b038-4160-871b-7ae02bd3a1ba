<?php

declare(strict_types=1);

namespace App\Classes\Constants;

class ServerParameters {
    final public const HTTP_METHOD_GET = 'GET';

    final public const HTTP_METHOD_POST = 'POST';

    final public const HTTP_METHOD_PATCH = 'PATCH';

    final public const HTTP_METHOD_PUT = 'PUT';

    final public const HTTP_METHOD_DELETE = 'DELETE';

    final public const HTTP_METHOD_OPTIONS = 'OPTIONS';

    final public const HTTP_STATUS_OK = 200;

    final public const HTTP_STATUS_BAD_REQUEST = 400;

    final public const HTTP_STATUS_UNAUTHORIZED = 401;

    final public const HTTP_STATUS_FORBIDDEN = 403;

    final public const HTTP_STATUS_NOT_FOUND = 404;

    final public const HTTP_STATUS_UNPROCESSABLE_ENTITY = 422;

    final public const HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;

    final public const HTTP_STATUS_BAD_GATEWAY = 502;

    final public const HTTP_STATUS_SERVICE_UNAVAILABLE = 503;

    final public const HTTP_STATUS_GATEWAY_TIMEOUT = 504;
}
